use clap::Parser;
use once_cell::sync::OnceCell;
use otel_common::{
    opentelemetry::{
        global,
        metrics::{Counter, Gauge},
    },
    process_info::get_system_memory,
};
use serde::{Deserialize, Serialize};
use tokio::sync::{SemaphorePermit, TryAcquireError};

use async_util::{await_with_async_timeout, AsyncTimeout, AsyncTimeoutError};
use util::ByteSize;

pub struct AsyncLimit {
    pub name: &'static str,
    semaphore: tokio::sync::Semaphore,
    capacity: usize,
    wait_time_ms_metric: Gauge<u64>,
    try_acquire_failed_metric: Counter<u64>,
}

impl std::fmt::Debug for AsyncLimit {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let available = self.semaphore.available_permits();
        let used = self.capacity - available;
        write!(
            f,
            "AsyncLimit(max_concurrent: {}, used: {}, available: {})",
            self.capacity, used, available
        )
    }
}

impl AsyncLimit {
    pub fn new(name: &'static str, max_concurrent: usize) -> Self {
        let meter = global::meter("brainstore");
        let wait_time_ms_metric = meter
            .u64_gauge(format!("brainstore.limits.{}.wait_time_ms", name))
            .build();
        let try_acquire_failed_metric = meter
            .u64_counter(format!("brainstore.limits.{}.try_acquire_failed", name))
            .build();
        Self {
            name,
            semaphore: tokio::sync::Semaphore::new(max_concurrent),
            capacity: max_concurrent,
            wait_time_ms_metric,
            try_acquire_failed_metric,
        }
    }
}

#[derive(Debug)]
pub enum AsyncLimitError {
    Closed,
    InsufficientCapacity,
    AsyncTimeout(AsyncTimeoutError),
}

impl std::error::Error for AsyncLimitError {}

impl std::fmt::Display for AsyncLimitError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{:?}", self)
    }
}

#[derive(Debug)]
pub enum TryAsyncLimitError {
    InsufficientCapacity,
    Closed,
    NoPermits,
}

impl From<TryAcquireError> for TryAsyncLimitError {
    fn from(e: TryAcquireError) -> Self {
        match e {
            TryAcquireError::NoPermits => TryAsyncLimitError::NoPermits,
            TryAcquireError::Closed => TryAsyncLimitError::Closed,
        }
    }
}

impl std::fmt::Display for TryAsyncLimitError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{:?}", self)
    }
}

impl std::error::Error for TryAsyncLimitError {}

impl AsyncLimit {
    pub async fn start_task(&self) -> Result<SemaphorePermit<'_>, AsyncLimitError> {
        let fut = async {
            match self.semaphore.acquire().await {
                Ok(permit) => Ok(permit),
                Err(_) => Err(AsyncLimitError::Closed),
            }
        };
        let start = std::time::Instant::now();
        match await_with_async_timeout(
            || format!("start_task {}", self.name).into(),
            fut,
            AsyncTimeout::default(),
            None,
        )
        .await
        {
            Ok(permit) => {
                let wait_time_ms = start.elapsed().as_millis();
                self.wait_time_ms_metric.record(wait_time_ms as u64, &[]);
                permit
            }
            Err(e) => Err(AsyncLimitError::AsyncTimeout(e)),
        }
    }

    pub fn try_start_task(&self) -> Result<SemaphorePermit<'_>, TryAsyncLimitError> {
        match self.semaphore.try_acquire() {
            Ok(permit) => Ok(permit),
            Err(e) => {
                if matches!(e, TryAcquireError::NoPermits) {
                    self.try_acquire_failed_metric.add(1, &[]);
                }
                Err(e.into())
            }
        }
    }

    pub async fn start_many(&self, num_tasks: u32) -> Result<SemaphorePermit<'_>, AsyncLimitError> {
        if num_tasks > self.capacity as u32 {
            return Err(AsyncLimitError::InsufficientCapacity);
        }
        let fut = async {
            match self.semaphore.acquire_many(num_tasks).await {
                Ok(permit) => Ok(permit),
                Err(_) => Err(AsyncLimitError::Closed),
            }
        };
        let start = std::time::Instant::now();
        match await_with_async_timeout(
            || format!("start_task_many {} num_tasks {}", self.name, num_tasks).into(),
            fut,
            AsyncTimeout::default(),
            None,
        )
        .await
        {
            Ok(permit) => {
                let wait_time_ms = start.elapsed().as_millis();
                self.wait_time_ms_metric.record(wait_time_ms as u64, &[]);
                permit
            }
            Err(e) => Err(AsyncLimitError::AsyncTimeout(e)),
        }
    }

    pub fn try_start_many(
        &self,
        num_tasks: u32,
    ) -> Result<SemaphorePermit<'_>, TryAsyncLimitError> {
        if num_tasks > self.capacity as u32 {
            self.try_acquire_failed_metric.add(1, &[]);
            return Err(TryAsyncLimitError::InsufficientCapacity);
        }
        match self.semaphore.try_acquire_many(num_tasks) {
            Ok(permit) => Ok(permit),
            Err(e) => {
                if matches!(e, TryAcquireError::NoPermits) {
                    self.try_acquire_failed_metric.add(1, &[]);
                }
                Err(e.into())
            }
        }
    }

    pub fn capacity(&self) -> usize {
        self.capacity
    }

    pub fn remaining_permits(&self) -> usize {
        self.semaphore.available_permits()
    }
}

#[derive(Debug)]
pub struct GlobalLimits {
    pub index_operations: AsyncLimit,
    pub object_store_read: AsyncLimit,
    pub object_store_write: AsyncLimit,
    pub wal_max_inflight_bytes: AsyncLimit,
    pub realtime_read_max_inflight_bytes: AsyncLimit,
    // These are direct bytes limits, not AsyncLimits
    pub realtime_read_max_per_query_bytes: usize,
    pub realtime_read_max_per_query_bytes_for_versioned_queries: usize,

    // These are not semaphores, but rather just the maximum number of connections to the database.
    pub wal_pool_size: usize,
    pub global_store_pool_size: usize,

    // Other global tuning parameters.
    pub global_store_max_params_per_query: usize,
    pub global_store_parallel_query_batch_size: usize,
    pub global_store_non_atomic_insert_batch_size: usize,
}

#[derive(Parser, Serialize, Deserialize, Debug, Clone)]
pub struct GlobalLimitArgs {
    // We obtain the limits for object store operations from
    // https://docs.aws.amazon.com/AmazonS3/latest/userguide/optimizing-performance.html.
    //
    // It seems that the rate limits apply per S3 partition (which is an internal concept), so the
    // defaults conservatively assume one partition.
    #[clap(
        long("max-concurrent-object-store-reads"),
        default_value_t = default_max_concurrent_object_store_reads(),
        help = "Maximum number of concurrent object store read operations",
        env = "BRAINSTORE_MAX_CONCURRENT_OBJECT_STORE_READS"
    )]
    #[serde(default = "default_max_concurrent_object_store_reads")]
    pub object_store_read: usize,

    #[clap(
        long("max-concurrent-object-store-writes"),
        default_value_t = default_max_concurrent_object_store_writes(),
        help = "Maximum number of concurrent object store write operations",
        env = "BRAINSTORE_MAX_CONCURRENT_OBJECT_STORE_WRITES"
    )]
    #[serde(default = "default_max_concurrent_object_store_writes")]
    pub object_store_write: usize,

    // Compactions are very expensive and tend to use a lot of memory, so we limit the maximum number
    // of concurrent WAL entries that can be compacted at once. The default is based on `default_max_num_wal_entries_per_compaction_iter`
    // (10,000) multiplied by the number of cores.
    #[clap(
        long("max-concurrent-index-operations"),
        default_value_t = default_max_concurrent_index_operations(),
        help = "Maximum number of concurrent index operations",
        env = "BRAINSTORE_MAX_CONCURRENT_INDEX_OPERATIONS"
    )]
    #[serde(default = "default_max_concurrent_index_operations")]
    pub index_operations: usize,

    #[arg(
        long,
        help = "The maximum number of connections to the database for the WAL",
        env = "BRAINSTORE_POSTGRES_WAL_POOL_SIZE",
        default_value_t= default_postgres_pool_size()
    )]
    #[serde(default = "default_postgres_pool_size")]
    pub wal_pool_size: usize,

    #[arg(
        long,
        help = "The maximum number of connections to the database for the global store",
        env = "BRAINSTORE_GLOBAL_STORE_POOL_SIZE",
        default_value_t= default_postgres_pool_size()
    )]
    #[serde(default = "default_postgres_pool_size")]
    pub global_store_pool_size: usize,

    #[clap(
        long,
        value_parser = ByteSize::parse_to_usize,
        default_value_t = default_wal_max_inflight(),
        help = format!("Maximum size of WAL entries in flight globally (defaults to {})", ByteSize::from(default_wal_max_inflight())),
        env = "BRAINSTORE_WAL_MAX_INFLIGHT"
    )]
    #[serde(default = "default_wal_max_inflight")]
    pub wal_max_inflight: usize,

    #[clap(
        long,
        value_parser = ByteSize::parse_to_usize,
        default_value_t = default_realtime_read_max_inflight(),
        help = format!("Maximum size of WAL entries in use by realtime reads globally (defaults to {}). These WAL entries are counted separately from the wal_max_inflight entries", ByteSize::from(default_realtime_read_max_inflight())),
        env = "BRAINSTORE_REALTIME_READ_MAX_INFLIGHT"
    )]
    #[serde(default = "default_realtime_read_max_inflight")]
    pub realtime_read_max_inflight: usize,

    #[clap(
        long,
        value_parser = ByteSize::parse_to_usize,
        default_value_t = default_realtime_read_max_per_query(),
        help = format!("Maximum size of WAL entries that can be read in a single realtime query (defaults to {})", ByteSize::from(default_realtime_read_max_per_query())),
        env = "BRAINSTORE_REALTIME_READ_MAX_PER_QUERY"
    )]
    #[serde(default = "default_realtime_read_max_per_query")]
    pub realtime_read_max_per_query: usize,

    #[clap(
        long,
        value_parser = ByteSize::parse_to_usize,
        default_value_t = default_realtime_read_max_per_query_for_versioned_queries(),
        help = format!("Maximum size of WAL entries that can be read in a single realtime query, for versioned queries (defaults to {})", ByteSize::from(default_realtime_read_max_per_query_for_versioned_queries())),
        env = "BRAINSTORE_REALTIME_READ_MAX_PER_QUERY"
    )]
    #[serde(default = "default_realtime_read_max_per_query_for_versioned_queries")]
    pub realtime_read_max_per_query_for_versioned_queries: usize,

    #[arg(
        long,
        help = "The maximum number of parameters for a single query to the postgres global store",
        env = "BRAINSTORE_GLOBAL_STORE_MAX_PARAMS_PER_QUERY",
        default_value_t= default_global_store_max_params_per_query()
    )]
    #[serde(default = "default_global_store_max_params_per_query")]
    pub global_store_max_params_per_query: usize,

    #[arg(
        long,
        help = "The parameter batch size for parallelizable queries to the postgres global store",
        env = "BRAINSTORE_GLOBAL_STORE_PARALLEL_QUERY_BATCH_SIZE",
        default_value_t= default_global_store_parallel_query_batch_size()
    )]
    #[serde(default = "default_global_store_parallel_query_batch_size")]
    pub global_store_parallel_query_batch_size: usize,

    #[arg(
        long,
        help = "The parameter batch size for non-atomic insert queries to the postgres global store",
        env = "BRAINSTORE_GLOBAL_STORE_NON_ATOMIC_INSERT_BATCH_SIZE",
        default_value_t= default_global_store_non_atomic_insert_batch_size()
    )]
    #[serde(default = "default_global_store_non_atomic_insert_batch_size")]
    pub global_store_non_atomic_insert_batch_size: usize,
}

fn default_max_concurrent_object_store_reads() -> usize {
    5500
}

fn default_max_concurrent_object_store_writes() -> usize {
    3500
}

fn clipped_memory_limit(lower_bound: usize, upper_bound: usize) -> usize {
    let machine_memory = get_system_memory();
    // Don't use more than 20% of the system memory.
    let machine_memory_limit = machine_memory / 5;
    std::cmp::max(
        std::cmp::min(upper_bound, machine_memory_limit),
        lower_bound,
    )
}

fn default_wal_max_inflight() -> usize {
    // Note for posterity: we used to have the upper bound at 10GB, but we
    // noticed that on some workloads, even when limiting the raw WAL bytes read
    // to 10GB, the serde-deserialized payloads would end up being much larger
    // than that and OOMing the machine. So we've reduced it to 1GB.
    let lower_bound = 1 * 1024 * 1024 * 1024;
    let upper_bound = 1 * 1024 * 1024 * 1024;
    clipped_memory_limit(lower_bound, upper_bound)
}

fn default_realtime_read_max_inflight() -> usize {
    // Even though we limit memory consumption for indexing operations, we have
    // noticed that customers often write large experiments and rely on realtime
    // reads to summarize them. So we set the realtime read limit to be higher (up
    // to 10GB). If we reduce this, then make sure to check with high-memory
    // experiment customers to make sure they're not affected.
    let lower_bound = 1 * 1024 * 1024 * 1024;
    let upper_bound = 10 * 1024 * 1024 * 1024;
    clipped_memory_limit(lower_bound, upper_bound)
}

fn default_realtime_read_max_per_query() -> usize {
    // 256MB
    256 * 1024 * 1024
}

fn default_realtime_read_max_per_query_for_versioned_queries() -> usize {
    // 2GB
    2 * 1024 * 1024 * 1024
}

pub fn default_postgres_pool_size() -> usize {
    // One per core, times 4 to allow for increased parallelism, e.g. splitting
    // a large query into multiple smaller queries.
    std::thread::available_parallelism().unwrap().get() * 4
}

pub fn default_index_threads_per_operation() -> usize {
    // Benchmarks (https://docs.google.com/spreadsheets/d/1zkDK2mivDtC-Kbz6AJ1DDj3vRWoBfCvczMd0cH1fIrM/edit?gid=0#gid=0)
    // suggest that 4 threads optimize throughput across compaction and merging.
    4
}

pub fn default_global_store_max_params_per_query() -> usize {
    // If the number of query params exceeds this, we'll batch the params into
    // multiple queries. We assume params are passed in bulk as arrays, so the limit
    // is on the order of the 1GB query text limit.
    //
    // Assuming our largest query params are the size of a UUID (36 chars), this
    // comes out to about 27.7 million params. To be conservative, we just use 1
    // million.
    //
    // This value is generally large enough that it should be okay to run batches
    // in serial.
    1000000
}

pub fn default_global_store_parallel_query_batch_size() -> usize {
    10000
}

pub fn default_global_store_non_atomic_insert_batch_size() -> usize {
    10000
}

fn calculate_memory_budget_per_indexing_operation(
    system_memory: usize,
    max_concurrent_operations: usize,
    threads_per_operation: usize,
) -> usize {
    // These constants are copy/pasted from Tantivy
    // https://github.com/braintrustdata/tantivy/blob/0.22.0-tweaks/src/indexer/index_writer.rs#L29
    // --------------------------------------------------------------------------------------------
    //
    // Size of the margin for the `memory_arena`. A segment is closed when the remaining memory
    // in the `memory_arena` goes below MARGIN_IN_BYTES.
    const MARGIN_IN_BYTES: usize = 1_000_000;

    // We impose the memory per thread to be at least 15 MB, as the baseline consumption is 12MB.
    const MEMORY_BUDGET_NUM_BYTES_MIN: usize = ((MARGIN_IN_BYTES as u32) * 15u32) as usize;
    const MEMORY_BUDGET_NUM_BYTES_MAX: usize = u32::MAX as usize - MARGIN_IN_BYTES;
    // --------------------------------------------------------------------------------------------

    let per_worker_memory = system_memory / max_concurrent_operations;

    // Empirically, about 1/3 of the memory is used by the indexing operation itself. Tantivy itself requires
    // about 15mb per thread and up to ~4GB per thread, so make sure we at least have that much.
    (per_worker_memory / 3).clamp(
        // These numbers are per thread, but our accounting is across threads, so multiply by the
        // number of threads per operation.
        MEMORY_BUDGET_NUM_BYTES_MIN * threads_per_operation,
        (MEMORY_BUDGET_NUM_BYTES_MAX - 1) * threads_per_operation,
    )
}

#[cfg(test)]
pub fn calculate_memory_budget_per_indexing_operation_for_test(
    system_memory: usize,
    max_concurrent_operations: usize,
    threads_per_operation: usize,
) -> usize {
    calculate_memory_budget_per_indexing_operation(
        system_memory,
        max_concurrent_operations,
        threads_per_operation,
    )
}

pub fn default_memory_budget_per_indexing_operation() -> usize {
    calculate_memory_budget_per_indexing_operation(
        get_system_memory(),
        default_max_concurrent_index_operations(),
        default_index_threads_per_operation(),
    )
}

pub fn default_max_concurrent_index_operations() -> usize {
    // We want to run about as many indexing operations as we have cores, with some over-commit
    // budget to account for I/O. Since certain operations, like compaction can use multiple threads,
    // we can compute this in terms of the number of cores and threads per operation. Interestingly,
    // tantivy hardcodes the number of merge threads to 4 as well, so this is a convenient default.
    let num_cores: usize = std::thread::available_parallelism().unwrap().into();
    let cpu_slots = 2 * num_cores / default_index_threads_per_operation();

    // Everything is mmap'd now, so we just slice this in terms of the memory we want to give
    // tantivy: 2gb per indexing operation.
    let memory_slots = get_system_memory() / (2 * 1024 * 1024 * 1024);

    // We want to run as many indexing operations as we have cores or memory slots, whichever is smaller.
    std::cmp::max(std::cmp::min(cpu_slots, memory_slots), 1)
}

impl Default for GlobalLimitArgs {
    fn default() -> Self {
        // This ensures that we'll pick up env vars.
        GlobalLimitArgs::parse_from(vec![""])
    }
}

static GLOBAL_LIMITS: OnceCell<GlobalLimits> = OnceCell::new();

pub fn get_or_reset_global_limits(args: Option<GlobalLimitArgs>) -> &'static GlobalLimits {
    GLOBAL_LIMITS.get_or_init(move || {
        let limits = args.unwrap_or_default();
        assert!(limits.wal_max_inflight > 0);
        assert!(limits.realtime_read_max_inflight > 0);
        // We allow limits.realtime_read_max_per_query to be 0, which means that
        // we don't read any data from the WAL in realtime.
        GlobalLimits {
            index_operations: AsyncLimit::new(
                "max_concurrent_compactions",
                limits.index_operations,
            ),
            object_store_read: AsyncLimit::new(
                "max_concurrent_object_store_reads",
                limits.object_store_read,
            ),
            object_store_write: AsyncLimit::new(
                "max_concurrent_object_store_writes",
                limits.object_store_write,
            ),
            wal_max_inflight_bytes: AsyncLimit::new(
                "wal_max_inflight_bytes",
                limits.wal_max_inflight,
            ),
            realtime_read_max_inflight_bytes: AsyncLimit::new(
                "realtime_read_max_inflight_bytes",
                limits.realtime_read_max_inflight,
            ),
            realtime_read_max_per_query_bytes: limits.realtime_read_max_per_query,
            realtime_read_max_per_query_bytes_for_versioned_queries: limits
                .realtime_read_max_per_query_for_versioned_queries,
            wal_pool_size: limits.wal_pool_size,
            global_store_pool_size: limits.global_store_pool_size,
            global_store_max_params_per_query: limits.global_store_max_params_per_query,
            global_store_parallel_query_batch_size: limits.global_store_parallel_query_batch_size,
            global_store_non_atomic_insert_batch_size: limits
                .global_store_non_atomic_insert_batch_size,
        }
    })
}

pub fn global_limits() -> &'static GlobalLimits {
    get_or_reset_global_limits(None)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_exceed_limit() {
        let limit = AsyncLimit::new("test", 3);
        let permit = limit.start_many(4).await;
        assert!(permit.is_err());

        let permit = limit.try_start_many(3).unwrap();
        assert_eq!(permit.num_permits(), 3);

        let permit2 = limit.try_start_many(1);
        assert!(permit2.is_err());
    }

    #[test]
    fn test_calculate_memory_budget_per_indexing_operation() {
        // Test with 8GB system memory, 4 concurrent operations, 4 threads per operation
        let budget = calculate_memory_budget_per_indexing_operation(
            8 * 1024 * 1024 * 1024, // 8GB
            4,                      // 4 concurrent operations
            4,                      // 4 threads per operation
        );

        // Per worker memory: 8GB / 4 = 2GB
        // Budget: 2GB / 3 = ~683MB
        // This should not be clamped as it's between min (60MB) and max (~16GB)
        assert_eq!(budget, (2 * 1024 * 1024 * 1024) / 3);

        // Test minimum clamping: 120MB system memory, 1 operation, 4 threads
        let budget = calculate_memory_budget_per_indexing_operation(
            120 * 1024 * 1024, // 120MB
            1,                 // 1 operation
            4,                 // 4 threads
        );

        // Per worker: 120MB, budget would be 40MB (120MB/3), which is less than min 60MB
        assert_eq!(budget, 15_000_000 * 4);

        // Test maximum clamping: 200GB system memory, 1 operation, 4 threads
        let budget = calculate_memory_budget_per_indexing_operation(
            200 * 1024usize.pow(3), // 200GB
            1,                      // 1 operation
            4,                      // 4 threads
        );

        // Per worker: 200GB, budget would be ~66GB, but max is ~4GB * 4 = ~16GB
        let expected_max = ((u32::MAX as usize - 1_000_000) - 1) * 4;
        assert_eq!(budget, expected_max);
    }
}
